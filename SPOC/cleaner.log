2025-08-15 02:14:57,294 INFO            SpoolCleaner|           SpoolCleaner| Checking cache for files older then 2025-08-14T02:14:57.294+0800; config limit: 1d 0h 0m
2025-08-15 02:14:57,294 INFO            SpoolCleaner|           SpoolCleaner| Checking cache for printed files older then 2025-08-14T02:14:57.294+0800; config limit: 1d 0h 0m
2025-08-15 02:14:57,294 INFO            SpoolCleaner| cheSpoolCleanerManager| Begin of spool cleaner run
2025-08-15 02:14:57,372 INFO            SpoolCleaner| cheSpoolCleanerManager| Count of jobs in one cycle: selected - 1, prepared to remove - 0, removed from spooler - 0.
2025-08-15 02:14:57,372 INFO            SpoolCleaner| cheSpoolCleanerManager| Selection ends after [00:00:00.078] and remove ends after [00:00:00.000] by speed 0.00 jobs/second.
2025-08-15 02:14:57,372 INFO            SpoolCleaner| cheSpoolCleanerManager| Count of jobs in one cycle: selected - 0, prepared to remove - 0, removed from spooler - 0.
2025-08-15 02:14:57,372 INFO            SpoolCleaner| cheSpoolCleanerManager| Selection ends after [00:00:00.000] and remove ends after [00:00:00.000] by speed 0.00 jobs/second.
2025-08-15 02:14:57,372 INFO            SpoolCleaner| cheSpoolCleanerManager| Count of jobs: selected jobs - 1, prepared to remove - 0, removed from spooler - 0.
2025-08-15 02:14:57,372 INFO            SpoolCleaner| cheSpoolCleanerManager| End of spool cleaner run after [00:00:00.078] by speed 0.00 jobs/second.
2025-08-15 05:14:58,167 INFO            SpoolCleaner|           SpoolCleaner| Checking cache for files older then 2025-08-14T05:14:58.167+0800; config limit: 1d 0h 0m
2025-08-15 05:14:58,167 INFO            SpoolCleaner|           SpoolCleaner| Checking cache for printed files older then 2025-08-14T05:14:58.167+0800; config limit: 1d 0h 0m
2025-08-15 05:14:58,167 INFO            SpoolCleaner| cheSpoolCleanerManager| Begin of spool cleaner run
2025-08-15 05:14:58,244 INFO            SpoolCleaner| cheSpoolCleanerManager| Count of jobs in one cycle: selected - 1, prepared to remove - 0, removed from spooler - 0.
2025-08-15 05:14:58,244 INFO            SpoolCleaner| cheSpoolCleanerManager| Selection ends after [00:00:00.077] and remove ends after [00:00:00.000] by speed 0.00 jobs/second.
2025-08-15 05:14:58,244 INFO            SpoolCleaner| cheSpoolCleanerManager| Count of jobs in one cycle: selected - 0, prepared to remove - 0, removed from spooler - 0.
2025-08-15 05:14:58,244 INFO            SpoolCleaner| cheSpoolCleanerManager| Selection ends after [00:00:00.000] and remove ends after [00:00:00.000] by speed 0.00 jobs/second.
2025-08-15 05:14:58,244 INFO            SpoolCleaner| cheSpoolCleanerManager| Count of jobs: selected jobs - 1, prepared to remove - 0, removed from spooler - 0.
2025-08-15 05:14:58,244 INFO            SpoolCleaner| cheSpoolCleanerManager| End of spool cleaner run after [00:00:00.077] by speed 0.00 jobs/second.
2025-08-15 08:14:58,862 INFO            SpoolCleaner|           SpoolCleaner| Checking cache for files older then 2025-08-14T08:14:58.862+0800; config limit: 1d 0h 0m
2025-08-15 08:14:58,862 INFO            SpoolCleaner|           SpoolCleaner| Checking cache for printed files older then 2025-08-14T08:14:58.862+0800; config limit: 1d 0h 0m
2025-08-15 08:14:58,862 INFO            SpoolCleaner| cheSpoolCleanerManager| Begin of spool cleaner run
2025-08-15 08:14:59,181 INFO            SpoolCleaner| actSpoolCleanerManager| Spool cleaner statistics in one cycle: [removed jobs: 4, delete from SPOC: 3 (--- jobs/sec), delete from spooler: 3 (--- jobs/sec), destroy on SPOC: 4 (--- jobs/sec)]
2025-08-15 08:14:59,181 INFO            SpoolCleaner| cheSpoolCleanerManager| Count of jobs in one cycle: selected - 5, prepared to remove - 4, removed from spooler - 4.
2025-08-15 08:14:59,181 INFO            SpoolCleaner| cheSpoolCleanerManager| Selection ends after [00:00:00.078] and remove ends after [00:00:00.241] by speed --- jobs/second.
2025-08-15 08:14:59,181 INFO            SpoolCleaner| cheSpoolCleanerManager| Count of jobs in one cycle: selected - 0, prepared to remove - 0, removed from spooler - 0.
2025-08-15 08:14:59,181 INFO            SpoolCleaner| cheSpoolCleanerManager| Selection ends after [00:00:00.000] and remove ends after [00:00:00.000] by speed 0.00 jobs/second.
2025-08-15 08:14:59,181 INFO            SpoolCleaner| cheSpoolCleanerManager| Count of jobs: selected jobs - 5, prepared to remove - 4, removed from spooler - 4.
2025-08-15 08:14:59,181 INFO            SpoolCleaner| cheSpoolCleanerManager| End of spool cleaner run after [00:00:00.319] by speed --- jobs/second.
2025-08-15 11:14:59,645 INFO            SpoolCleaner|           SpoolCleaner| Checking cache for files older then 2025-08-14T11:14:59.645+0800; config limit: 1d 0h 0m
2025-08-15 11:14:59,645 INFO            SpoolCleaner|           SpoolCleaner| Checking cache for printed files older then 2025-08-14T11:14:59.645+0800; config limit: 1d 0h 0m
2025-08-15 11:14:59,645 INFO            SpoolCleaner| cheSpoolCleanerManager| Begin of spool cleaner run
2025-08-15 11:15:13,558 INFO            SpoolCleaner| actSpoolCleanerManager| Spool cleaner statistics in one cycle: [removed jobs: 243, delete from SPOC: 133 (133.00 jobs/sec), delete from spooler: 133 (--- jobs/sec), destroy on SPOC: 243 (--- jobs/sec)]
2025-08-15 11:15:13,558 INFO            SpoolCleaner| cheSpoolCleanerManager| Count of jobs in one cycle: selected - 246, prepared to remove - 245, removed from spooler - 243.
2025-08-15 11:15:13,558 INFO            SpoolCleaner| cheSpoolCleanerManager| Selection ends after [00:00:00.093] and remove ends after [00:00:13.820] by speed 18.69 jobs/second.
2025-08-15 11:15:13,558 INFO            SpoolCleaner| cheSpoolCleanerManager| Count of jobs in one cycle: selected - 0, prepared to remove - 0, removed from spooler - 0.
2025-08-15 11:15:13,558 INFO            SpoolCleaner| cheSpoolCleanerManager| Selection ends after [00:00:00.000] and remove ends after [00:00:00.000] by speed 0.00 jobs/second.
2025-08-15 11:15:13,558 INFO            SpoolCleaner| cheSpoolCleanerManager| Count of jobs: selected jobs - 246, prepared to remove - 245, removed from spooler - 243.
2025-08-15 11:15:13,558 INFO            SpoolCleaner| cheSpoolCleanerManager| End of spool cleaner run after [00:00:13.913] by speed 18.69 jobs/second.
2025-08-15 14:15:14,201 INFO            SpoolCleaner|           SpoolCleaner| Checking cache for files older then 2025-08-14T14:15:14.201+0800; config limit: 1d 0h 0m
2025-08-15 14:15:14,201 INFO            SpoolCleaner|           SpoolCleaner| Checking cache for printed files older then 2025-08-14T14:15:14.201+0800; config limit: 1d 0h 0m
2025-08-15 14:15:14,201 INFO            SpoolCleaner| cheSpoolCleanerManager| Begin of spool cleaner run
2025-08-15 14:15:34,081 INFO            SpoolCleaner| actSpoolCleanerManager| Spool cleaner statistics in one cycle: [removed jobs: 337, delete from SPOC: 175 (58.33 jobs/sec), delete from spooler: 175 (--- jobs/sec), destroy on SPOC: 337 (--- jobs/sec)]
2025-08-15 14:15:34,081 INFO            SpoolCleaner| cheSpoolCleanerManager| Count of jobs in one cycle: selected - 341, prepared to remove - 340, removed from spooler - 337.
2025-08-15 14:15:34,081 INFO            SpoolCleaner| cheSpoolCleanerManager| Selection ends after [00:00:00.125] and remove ends after [00:00:19.755] by speed 17.74 jobs/second.
2025-08-15 14:15:34,081 INFO            SpoolCleaner| cheSpoolCleanerManager| Count of jobs in one cycle: selected - 0, prepared to remove - 0, removed from spooler - 0.
2025-08-15 14:15:34,081 INFO            SpoolCleaner| cheSpoolCleanerManager| Selection ends after [00:00:00.000] and remove ends after [00:00:00.000] by speed 0.00 jobs/second.
2025-08-15 14:15:34,081 INFO            SpoolCleaner| cheSpoolCleanerManager| Count of jobs: selected jobs - 341, prepared to remove - 340, removed from spooler - 337.
2025-08-15 14:15:34,081 INFO            SpoolCleaner| cheSpoolCleanerManager| End of spool cleaner run after [00:00:19.880] by speed 17.74 jobs/second.
