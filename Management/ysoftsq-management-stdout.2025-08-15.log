
2025-08-15 14:23:16 Apache Commons Daemon procrun stdout initialized.

__  __ _____ ____  ____________   _____ ___    ________________      _____    ____  ________  _   ________
\ \/ // ___// __ \/ ____/_  __/  / ___//   |  / ____/ ____/ __ \    / ___/   / __ \/  _/ __ \/ | / / ____/
 \  / \__ \/ / / / /_    / /     \__ \/ /| | / /_  / __/ / / / /   / __ \   / / / // // / / /  |/ / __/
 / / ___/ / /_/ / __/   / /     ___/ / ___ |/ __/ / /___/ /_/ /   / /_/ /  / /_/ // // /_/ / /|  / /___
/_/ /____/\____/_/     /_/     /____/_/  |_/_/   /_____/\___\_\   \____/  /_____/___/\____/_/ |_/_____/


2025-08-15 14:24:37,866 main INFO Log4j appears to be running in a Servlet environment, but there's no log4j-web module available. If you want better web container support, please add the log4j-web JAR to your web archive or server lib directory.
2025-08-15 14:24:37,928 main INFO Log4j appears to be running in a Servlet environment, but there's no log4j-web module available. If you want better web container support, please add the log4j-web JAR to your web archive or server lib directory.
