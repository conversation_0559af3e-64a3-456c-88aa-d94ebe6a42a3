
2025-08-15 14:23:16 Apache Commons Daemon procrun stderr initialized.
WARNING: An illegal reflective access operation has occurred
WARNING: Illegal reflective access by com.ysoft.safeq.management.license.service.activation.generator.WindowsRegistryIdGenerator (file:/C:/SafeQ6/Management/tomcat/webapps/ROOT/WEB-INF/lib/license-6.0.84.1.jar) to method java.util.prefs.WindowsPreferences.openKey(byte[],int,int)
WARNING: Please consider reporting this to the maintainers of com.ysoft.safeq.management.license.service.activation.generator.WindowsRegistryIdGenerator
WARNING: Use --illegal-access=warn to enable warnings of further illegal reflective access operations
WARNING: All illegal access operations will be denied in a future release
r log
INFO: OS Name:               Windows Server 2019
Aug 15, 2025 2:23:17 PM org.apache.catalina.startup.VersionLoggerListener log
INFO: OS Version:            10.0
Aug 15, 2025 2:23:17 PM org.apache.catalina.startup.VersionLoggerListener log
INFO: Architecture:          amd64
Aug 15, 2025 2:23:17 PM org.apache.catalina.startup.VersionLoggerListener log
INFO: Java Home:             C:\SafeQ6\Management\java
Aug 15, 2025 2:23:17 PM org.apache.catalina.startup.VersionLoggerListener log
INFO: JVM Version:           11.0.15+9-LTS
Aug 15, 2025 2:23:17 PM org.apache.catalina.startup.VersionLoggerListener log
INFO: JVM Vendor:            Red Hat, Inc.
Aug 15, 2025 2:23:17 PM org.apache.catalina.startup.VersionLoggerListener log
INFO: CATALINA_BASE:         C:\SafeQ6\Management\tomcat
Aug 15, 2025 2:23:17 PM org.apache.catalina.startup.VersionLoggerListener log
INFO: CATALINA_HOME:         C:\SafeQ6\Management\tomcat
Aug 15, 2025 2:23:17 PM org.apache.catalina.startup.VersionLoggerListener log
INFO: Command line argument: -XX:+HeapDumpOnOutOfMemoryError
Aug 15, 2025 2:23:17 PM org.apache.catalina.startup.VersionLoggerListener log
INFO: Command line argument: -Dlogging.config=C:\SafeQ6\Management/conf/log4j2.xml
Aug 15, 2025 2:23:17 PM org.apache.catalina.startup.VersionLoggerListener log
INFO: Command line argument: -Djavax.net.ssl.trustStore=C:\SafeQ6\Management/conf/ssl-truststore
Aug 15, 2025 2:23:17 PM org.apache.catalina.startup.VersionLoggerListener log
INFO: Command line argument: -Djavax.net.ssl.keyStore=C:\SafeQ6\Management/conf/ssl-keystore
Aug 15, 2025 2:23:17 PM org.apache.catalina.startup.VersionLoggerListener log
INFO: Command line argument: -Djavax.net.ssl.keyStorePassword=39nrqoge332fgomeer3405
Aug 15, 2025 2:23:17 PM org.apache.catalina.startup.VersionLoggerListener log
INFO: Command line argument: -Djava.io.tmpdir=C:\SafeQ6\Management/server/temp
Aug 15, 2025 2:23:17 PM org.apache.catalina.startup.VersionLoggerListener log
INFO: Command line argument: -Dfile.encoding=UTF-8
Aug 15, 2025 2:23:17 PM org.apache.catalina.startup.VersionLoggerListener log
INFO: Command line argument: -Dorg.apache.jasper.compiler.Parser.STRICT_QUOTE_ESCAPING=false
Aug 15, 2025 2:23:17 PM org.apache.catalina.startup.VersionLoggerListener log
INFO: Command line argument: -Djava.locale.providers=COMPAT, CLDR
Aug 15, 2025 2:23:17 PM org.apache.catalina.startup.VersionLoggerListener log
INFO: Command line argument: exit
Aug 15, 2025 2:23:17 PM org.apache.catalina.startup.VersionLoggerListener log
INFO: Command line argument: abort
Aug 15, 2025 2:23:17 PM org.apache.catalina.startup.VersionLoggerListener log
INFO: Command line argument: -Xms128m
Aug 15, 2025 2:23:17 PM org.apache.catalina.startup.VersionLoggerListener log
INFO: Command line argument: -Xmx1024m
Aug 15, 2025 2:23:17 PM org.apache.catalina.core.AprLifecycleListener lifecycleEvent
INFO: Loaded Apache Tomcat Native library [1.2.36] using APR version [1.7.2].
Aug 15, 2025 2:23:17 PM org.apache.catalina.core.AprLifecycleListener lifecycleEvent
INFO: APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
Aug 15, 2025 2:23:17 PM org.apache.catalina.core.AprLifecycleListener lifecycleEvent
INFO: APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
Aug 15, 2025 2:23:17 PM org.apache.catalina.core.AprLifecycleListener initializeSSL
INFO: OpenSSL successfully initialized [OpenSSL 1.1.1t  7 Feb 2023]
Aug 15, 2025 2:23:17 PM org.apache.coyote.AbstractProtocol init
INFO: Initializing ProtocolHandler ["http-nio-80"]
Aug 15, 2025 2:23:17 PM org.apache.coyote.http11.AbstractHttp11Protocol configureUpgradeProtocol
INFO: The ["https-jsse-nio2-443"] connector has been configured to support negotiation to [h2] via ALPN
Aug 15, 2025 2:23:17 PM org.apache.coyote.AbstractProtocol init
INFO: Initializing ProtocolHandler ["https-jsse-nio2-443"]
Aug 15, 2025 2:23:18 PM org.apache.tomcat.util.net.SSLUtilBase checkTrustStoreEntries
WARNING: The trusted certificate with alias [trktrustelektroniksertifikahizmetsalaycsh5] and DN [CN=TÜRKTRUST Elektronik Sertifika Hizmet Sağlayıcısı H5, O=TÜRKTRUST Bilgi İletişim ve Bilişim Güvenliği Hizmetleri A.Ş., L=Ankara, C=TR] is not valid due to [NotAfter: Fri Apr 28 16:07:01 CST 2023]. Certificates signed by this trusted certificate WILL be accepted
Aug 15, 2025 2:23:18 PM org.apache.tomcat.util.net.SSLUtilBase checkTrustStoreEntries
WARNING: The trusted certificate with alias [staatdernederlandenrootca-g2] and DN [CN=Staat der Nederlanden Root CA - G2, O=Staat der Nederlanden, C=NL] is not valid due to [NotAfter: Wed Mar 25 19:03:10 CST 2020]. Certificates signed by this trusted certificate WILL be accepted
Aug 15, 2025 2:23:18 PM org.apache.tomcat.util.net.SSLUtilBase checkTrustStoreEntries
WARNING: The trusted certificate with alias [e-tugracertificationauthority] and DN [CN=E-Tugra Certification Authority, OU=E-Tugra Sertifikasyon Merkezi, O=E-Tuğra EBG Bilişim Teknolojileri ve Hizmetleri A.Ş., L=Ankara, C=TR] is not valid due to [NotAfter: Fri Mar 03 20:09:48 CST 2023]. Certificates signed by this trusted certificate WILL be accepted
Aug 15, 2025 2:23:18 PM org.apache.tomcat.util.net.SSLUtilBase checkTrustStoreEntries
WARNING: The trusted certificate with alias [addtrustexternalroot] and DN [CN=AddTrust External CA Root, OU=AddTrust External TTP Network, O=AddTrust AB, C=SE] is not valid due to [NotAfter: Sat May 30 18:48:38 CST 2020]. Certificates signed by this trusted certificate WILL be accepted
Aug 15, 2025 2:23:18 PM org.apache.tomcat.util.net.SSLUtilBase checkTrustStoreEntries
WARNING: The trusted certificate with alias [dstrootcax3] and DN [CN=DST Root CA X3, O=Digital Signature Trust Co.] is not valid due to [NotAfter: Thu Sep 30 22:01:15 CST 2021]. Certificates signed by this trusted certificate WILL be accepted
Aug 15, 2025 2:23:18 PM org.apache.tomcat.util.net.SSLUtilBase checkTrustStoreEntries
WARNING: The trusted certificate with alias [soneraclass2rootca] and DN [CN=Sonera Class2 CA, O=Sonera, C=FI] is not valid due to [NotAfter: Tue Apr 06 15:29:40 CST 2021]. Certificates signed by this trusted certificate WILL be accepted
Aug 15, 2025 2:23:18 PM org.apache.tomcat.util.net.SSLUtilBase checkTrustStoreEntries
WARNING: The trusted certificate with alias [globalsignrootca-r2] and DN [CN=GlobalSign, O=GlobalSign, OU=GlobalSign Root CA - R2] is not valid due to [NotAfter: Wed Dec 15 16:00:00 CST 2021]. Certificates signed by this trusted certificate WILL be accepted
Aug 15, 2025 2:23:18 PM org.apache.tomcat.util.net.SSLUtilBase checkTrustStoreEntries
WARNING: The trusted certificate with alias [baltimorecybertrustroot] and DN [CN=Baltimore CyberTrust Root, OU=CyberTrust, O=Baltimore, C=IE] is not valid due to [NotAfter: Tue May 13 07:59:00 CST 2025]. Certificates signed by this trusted certificate WILL be accepted
Aug 15, 2025 2:23:18 PM org.apache.tomcat.util.net.SSLUtilBase checkTrustStoreEntries
WARNING: The trusted certificate with alias [geotrustglobalca] and DN [CN=GeoTrust Global CA, O=GeoTrust Inc., C=US] is not valid due to [NotAfter: Sat May 21 12:00:00 CST 2022]. Certificates signed by this trusted certificate WILL be accepted
Aug 15, 2025 2:23:18 PM org.apache.tomcat.util.net.SSLUtilBase checkTrustStoreEntries
WARNING: The trusted certificate with alias [visaecommerceroot] and DN [CN=Visa eCommerce Root, OU=Visa International Service Association, O=VISA, C=US] is not valid due to [NotAfter: Fri Jun 24 08:16:12 CST 2022]. Certificates signed by this trusted certificate WILL be accepted
Aug 15, 2025 2:23:18 PM org.apache.tomcat.util.net.SSLUtilBase checkTrustStoreEntries
WARNING: The trusted certificate with alias [quovadisrootca] and DN [CN=QuoVadis Root Certification Authority, OU=Root Certification Authority, O=QuoVadis Limited, C=BM] is not valid due to [NotAfter: Thu Mar 18 02:33:33 CST 2021]. Certificates signed by this trusted certificate WILL be accepted
Aug 15, 2025 2:23:18 PM org.apache.tomcat.util.net.SSLUtilBase checkTrustStoreEntries
WARNING: The trusted certificate with alias [staatdernederlandenevrootca] and DN [CN=Staat der Nederlanden EV Root CA, O=Staat der Nederlanden, C=NL] is not valid due to [NotAfter: Thu Dec 08 19:10:28 CST 2022]. Certificates signed by this trusted certificate WILL be accepted
Aug 15, 2025 2:23:18 PM org.apache.tomcat.util.net.SSLUtilBase checkTrustStoreEntries
WARNING: The trusted certificate with alias [securitycommunicationrootca] and DN [OU=Security Communication RootCA1, O=SECOM Trust.net, C=JP] is not valid due to [NotAfter: Sat Sep 30 12:20:49 CST 2023]. Certificates signed by this trusted certificate WILL be accepted
Aug 15, 2025 2:23:18 PM org.apache.tomcat.util.net.SSLUtilBase checkTrustStoreEntries
WARNING: The trusted certificate with alias [hongkongpostrootca1] and DN [CN=Hongkong Post Root CA 1, O=Hongkong Post, C=HK] is not valid due to [NotAfter: Mon May 15 12:52:29 CST 2023]. Certificates signed by this trusted certificate WILL be accepted
Aug 15, 2025 2:23:18 PM org.apache.tomcat.util.net.SSLUtilBase checkTrustStoreEntries
WARNING: The trusted certificate with alias [cybertrustglobalroot] and DN [CN=Cybertrust Global Root, O="Cybertrust, Inc"] is not valid due to [NotAfter: Wed Dec 15 16:00:00 CST 2021]. Certificates signed by this trusted certificate WILL be accepted
Aug 15, 2025 2:23:18 PM org.apache.tomcat.util.net.SSLUtilBase checkTrustStoreEntries
WARNING: The trusted certificate with alias [trustisfpsrootca] and DN [OU=Trustis FPS Root CA, O=Trustis Limited, C=GB] is not valid due to [NotAfter: Sun Jan 21 19:36:54 CST 2024]. Certificates signed by this trusted certificate WILL be accepted
Aug 15, 2025 2:23:18 PM org.apache.tomcat.util.net.AbstractEndpoint logCertificate
INFO: Connector [https-jsse-nio2-443], TLS virtual host [_default_], certificate type [UNDEFINED] configured from [../conf/ssl-keystore] using alias [safeqtomcat] and with trust store [C:\SafeQ6\Management/conf/ssl-truststore]
Aug 15, 2025 2:23:18 PM org.apache.catalina.startup.Catalina load
INFO: Server initialization in [1439] milliseconds
Aug 15, 2025 2:23:18 PM org.apache.catalina.core.StandardService startInternal
INFO: Starting service [Catalina]
Aug 15, 2025 2:23:18 PM org.apache.catalina.core.StandardEngine startInternal
INFO: Starting Servlet engine: [Apache Tomcat/9.0.72]
Aug 15, 2025 2:23:18 PM org.apache.catalina.startup.HostConfig deployWAR
INFO: Deploying web application archive [C:\SafeQ6\Management\tomcat\webapps\ROOT.war]
Aug 15, 2025 2:23:36 PM org.apache.jasper.servlet.TldScanner scanJars
INFO: At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
Aug 15, 2025 2:23:36 PM org.apache.catalina.core.ApplicationContext log
INFO: 2 Spring WebApplicationInitializers detected on classpath
Exception in thread "https-jsse-nio2-443-exec-10" java.nio.channels.WritePendingException
	at java.base/sun.nio.ch.AsynchronousSocketChannelImpl.write(AsynchronousSocketChannelImpl.java:353)
	at java.base/sun.nio.ch.AsynchronousSocketChannelImpl.write(AsynchronousSocketChannelImpl.java:400)
	at org.apache.tomcat.util.net.SecureNio2Channel$4.completed(SecureNio2Channel.java:1250)
	at org.apache.tomcat.util.net.SecureNio2Channel$4.completed(SecureNio2Channel.java:1244)
	at java.base/sun.nio.ch.Invoker.invokeUnchecked(Invoker.java:127)
	at java.base/sun.nio.ch.Invoker$2.run(Invoker.java:219)
	at java.base/sun.nio.ch.AsynchronousChannelGroupImpl$1.run(AsynchronousChannelGroupImpl.java:112)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
